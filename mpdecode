#!/usr/bin/env php

<?php

$src_arg = $argv[1];
$src_dir = realpath(pathinfo($src_arg, PATHINFO_DIRNAME));

// Check if the source is a directory
if (is_dir($src_arg)) {
    // If it's a directory, search for all JPG/JPEG files within it
    $pattern = rtrim($src_arg, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . "*.{jpg,jpeg,JPG,JPEG}";
    echo "Directory detected, using pattern: $pattern\n";
} else {
    // Otherwise use the original pattern
    $pattern = $src_arg;
}

echo "Scanning for files...\n";

foreach (glob($pattern, GLOB_BRACE) as $src) {
  $file = realpath($src);
  if (!is_dir($file) && in_array(strtoupper(pathinfo($file, PATHINFO_EXTENSION)), ["JPEG", "JPG"])) {

    echo "\tProcessing: " . $file . "\n";

    $filesize = filesize($file);
    echo "\t\tFile size: " . $filesize . "\n";

    $handle = fopen($file, "rb");
    $data = fread($handle, $filesize);
    fclose($handle);

    $mp4_start_pos = strpos($data, "ftyp");

    if ($mp4_start_pos !== FALSE) {
      $mp4_start_pos -= 4; # the real beginning of the mp4 starts 4 bytes before "ftyp"

      $jpg_end_pos = strrpos(substr($data, 0, $mp4_start_pos), "\xFF\xD9");

      if ($jpg_end_pos !== FALSE) {
        $jpg_end_pos += 2; # account for the length of the search string

        $output_base = $src_dir . DIRECTORY_SEPARATOR . pathinfo($file, PATHINFO_FILENAME);
        echo "\t\tSaving photo...\n";
        file_put_contents($output_base . "_photo.jpg", substr($data, 0, $jpg_end_pos));
        echo "\t\tSaving video...\n";
        file_put_contents($output_base . "_video.mp4", substr($data, $mp4_start_pos));

      } else {
        echo "\t\tSKIPPING - File appears to contain an MP4 but the no valid JPG EOI segment could be found.\n";
      }

    } else {
      echo "\t\tSKIPPING - File does not appear to be a Google motion photo.\n";
    }

  }

}

echo "Done.\n";

?>
