#!/usr/bin/env bash

# debug flag
set -x


#################  IMPROVEMENTS ################
# - detect if audio file has been downloaded before and exit
# - grab filename for each audio file rather than rely on last touched 
#   * this  matters if there is more than one process running
################################################


# we want to download the best audio
# and convert it afterward to mp3

best_quality=$(python3 /usr/local/bin/youtube-dl -F "$1" | grep 'audio only'| tail -1 | cut -d' ' -f1)
python3 /usr/local/bin/youtube-dl -f "$best_quality" "$1"
cd ~/youtube-extracts
last_download=$(ls -t | grep -v '\.part' | head -1)
extension="${last_download##*.}"
filename="${last_download%.*}"

new_filename=$(echo "$filename"'.mp3' | sed -E 's/\s*\(.*?\)//' | sed 's/ - /-/g' | sed 's/&/and/g' | tr ' ' '_' | tr -d "'" | tr -d '"')

ffmpeg -i "$last_download" "$new_filename"
if [ $? = 0 ]; then
  rm "$last_download"
else
  echo "ffmpeg failed, not removing original file"
fi



