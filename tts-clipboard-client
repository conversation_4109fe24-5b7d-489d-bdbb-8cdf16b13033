#!/home/<USER>/.pyenv/versions/3.12.9/bin/python3.12

import socket
import json
import sys
import tkinter as tk
from pathlib import Path
from threading import Thread

SOCKET_PATH = Path("/tmp/tts_clipboard.sock")

# combine the following voices to create a preferred voice
preferred_voice = 'af_bella,af_jessica,am_micha<PERSON>,hf_alpha'

class CancelWindow:
    def __init__(self, sock):
        self.root = tk.Tk()
        self.root.title("TTS Control")
        self.root.geometry("250x120")
        self.sock = sock

        # Center the window
        self.root.eval('tk::PlaceWindow . center')

        # Keep window on top
        self.root.attributes('-topmost', True)

        # Status label for showing buffering/playing status
        self.status_label = tk.Label(
            self.root,
            text="Connecting...",
            font=("Arial", 10)
        )
        self.status_label.pack(pady=5)

        # Progress label for buffering percentage
        self.progress_label = tk.Label(
            self.root,
            text="",
            font=("Arial", 9),
            fg="gray"
        )
        self.progress_label.pack()

        self.cancel_button = tk.Button(
            self.root,
            text="Stop Reading",
            command=self.cancel,
            height=2,
            width=20
        )
        self.cancel_button.pack(expand=True, pady=5)

        # Start listening for completion in a separate thread
        self.listen_thread = Thread(target=self.listen_for_completion)
        self.listen_thread.daemon = True
        self.listen_thread.start()

    def listen_for_completion(self):
        try:
            while True:
                data = self.sock.recv(1024).decode('utf-8')
                if not data:
                    break

                response = json.loads(data)
                status = response.get('status')

                if status == 'buffering':
                    progress = response.get('progress', 0)
                    total = response.get('total', 1)
                    percentage = response.get('percentage', 0)

                    self.root.after(0, lambda: self.status_label.config(text="Buffering audio..."))
                    self.root.after(0, lambda p=percentage: self.progress_label.config(text=f"{p}% buffered"))

                elif status == 'playback_started':
                    msg = response.get('msg', '')
                    self.root.after(0, lambda: self.status_label.config(text="Playing audio"))
                    self.root.after(0, lambda m=msg: self.progress_label.config(text=f"{m}"))

                elif status == 'complete':
                    self.root.after(0, self.root.destroy)
                    break
        except Exception as e:
            # Socket closed or error occurred
            print(f"Error in listen_for_completion: {e}")
            self.root.after(0, self.root.destroy)

    def cancel(self):
        try:
            # Send cancel signal to server
            cancel_sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            cancel_sock.connect(str(SOCKET_PATH))
            data = json.dumps({'command': 'cancel'})
            cancel_sock.send(data.encode('utf-8'))
            cancel_sock.close()

            # Close the main socket as well
            if self.sock:
                self.sock.close()
                self.sock = None

        except Exception as e:
            print(f"Error sending cancel signal: {e}")
        finally:
            self.root.destroy()

    def show(self):
        self.root.mainloop()

def trigger_tts(voice=preferred_voice):
    try:
        # Start TTS
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.connect(str(SOCKET_PATH))
        data = json.dumps({'command': 'speak', 'voice': voice})
        sock.send(data.encode('utf-8'))

        # Show cancel window
        cancel_window = CancelWindow(sock)
        cancel_window.show()

        # Cleanup
        sock.close()

    except Exception as e:
        print(f"Error connecting to TTS service: {e}")

if __name__ == "__main__":
    # Get voice from command line argument if provided
    voice = sys.argv[1] if len(sys.argv) > 1 else preferred_voice
    trigger_tts(voice)
