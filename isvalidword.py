#!/usr/bin/env python3

# this script is indended to be called from within a bash script
# the lastpdf.txt file SHOULD exist and be the output of pdf2text as
# generated in the parent bash process

import re
file = open("/usr/share/dict/words", "r")
validwords = re.sub("[^\w]", " ",  file.read()).split()
file.close()

def is_word(word):
  return word.lower() in validwords

file = open("lastpdf.txt", "r")
ourwords = re.sub("[^\w]", " ",  file.read()).split()
file.close()

for word in ourwords:
  if word.lower() in validwords:
    print(word + ' ', end='')

