#!/usr/bin/env bash

python3 /usr/local/bin/yt-dlp -f 'ba' "$1" -o '%(title)s.%(ext)s'       
#| perl -pe  's/(.*)? ｜ (.*)?\.(.*)/\2 - \1.\3/'



# this was the rename & move in 1 step via a for loop as I was fetching podcasts ... 

#echo;for i in `ls *.webm`; do newname="`echo $i | perl -pe  's/(.*)? ｜ (.*)?\.(.*)/\2 - \1.\3/'`";echo "Moving: $newname";mv "$i" "$newname"; mv "$newname" /run/user/1000/gvfs/mtp\:host\=Google_Pixel_XL_HT76T0203004/Internal\ shared\ storage/Music/Lex\ Podcasts 2>/dev/null; done;echo;
