#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e
set -x 

# Check if the repository name is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <repository-name>"
  exit 1
fi

# Variables
REPO_NAME=$1
GITHUB_USERNAME="hotpocket"

# Initialize a new Git repository
echo "Initializing a new Git repository..."
git init

# Stage all changes
echo "Staging all changes..."
git add -A

# Commit the changes
echo "Creating the initial commit..."
git commit -m "Initial commit"

# Create a new GitHub repository using the GitHub CLI
echo "Creating a new repository on GitHub..."
gh repo create "$REPO_NAME" --private --confirm

# Add the remote repository
REMOTE_URL="**************:$GITHUB_USERNAME/$REPO_NAME.git"
echo "Adding the remote repository: $REMOTE_URL"
git remote add origin "$REMOTE_URL"

# Push the code to the remote repository
echo "Pushing the code to GitHub..."
git push -u origin main

echo "Repository '$REPO_NAME' created and pushed successfully!"

