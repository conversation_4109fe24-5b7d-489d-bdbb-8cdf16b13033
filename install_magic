#install Wine
sudo dpkg --add-architecture i386
wget -nc https://dl.winehq.org/wine-builds/Release.key
sudo apt-key add Release.key
sudo apt-add-repository https://dl.winehq.org/wine-builds/ubuntu/
sudo apt-get update
sudo apt-get install --install-recommends winehq-devel
export WINEARCH=win32
sudo apt-get install winetricks

#use "Windows 7"
WINEPREFIX=~/.wine32 winecfg

#Select the Default WinePrefix -> Install DLL or Windows Component -> dotnet4.6.1
#It will prompt and install a lot, in the end it should correctly install net 4.6.1
WINEPREFIX="$HOME/.wine32" WINEARCH=win32 winetricks

#Install and configure some more things
WINEPREFIX=~/.wine32 winetricks comctl32
WINEPREFIX=~/.wine32 winetricks corefonts
#ddr=gdi instead of ddr=opengl might improve rendering 
WINEPREFIX=~/.wine32 winetricks ddr=gdi

#Install MTGO
WINEPREFIX="$HOME/.wine32" setup.exe

#Run MTGO (you can also run with WINEPREFIX="$HOME/.wine32" winefile)
WINEPREFIX="$HOME/.wine32" wine start Magic\ The\ Gathering\ Online\ .appref-ms
