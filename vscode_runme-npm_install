# to be sourced into the current shell

nvm install
npm config set @buf:registry https://buf.build/gen/npm/v1

## install proj deps
export GITHUB_REF_NAME=$(git branch --show-current)
export GITHUB_TOKEN=$(gh auth token)
export EXTENSION_BASENAME=$(node -p 'process.cwd().split("/").pop().split("-")[1]')
cp -f "assets/$EXTENSION_BASENAME-icon.gif" "assets/icon.gif"
cp -f "assets/$EXTENSION_BASENAME-logo-open-dark.svg" "assets/logo-open-dark.svg"
cp -f "assets/$EXTENSION_BASENAME-logo-open-light.svg" "assets/logo-open-light.svg"
cp -f "assets/$EXTENSION_BASENAME-logo-sidebar.svg" "assets/logo-sidebar.svg"
NODE_OPTIONS='' npm install --include=dev

## download the WASM binary
export GITHUB_REF_NAME=$(git branch --show-current)
GITHUB_TOKEN=$(gh auth token) npm run download:wasm
npm run prepare-binary -- -f tar

