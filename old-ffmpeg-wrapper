#!/bin/bash

# -----------------------------------------------------------------
# -- this will cancel ANY pipe and replace it w/ progress pipe!! --
# -----------------------------------------------------------------

#set -x 

REAL_FFMPEG_PATH="/usr/bin/ffmpeg"

# Name of your progress parser function/script
PROGRESS_PARSER_CMD="ffmpeg_progress_parser" # Ensure this function is sourced in your shell or is an executable in PATH

# Check if stdout or stderr is a TTY
# -t 1 checks if stdout is a TTY
# -t 2 checks if stderr is a TTY
# If both are TTYs, then we are likely in an interactive terminal session
if [ -t 1 ] && [ -t 2 ]; then
    # Not piped or redirected to a non-TTY. Run ffmpeg normally.
    # We explicitly redirect stderr of the real ffmpeg to the terminal's stderr
    # to avoid issues where the wrapper might capture it implicitly.
    exec "$REAL_FFMPEG_PATH" "-hide_banner" "$@" 2>&1
else
    # Output is piped or redirected to a file.
    # Add the progress arguments and pipe to our parser.

    # Build the base command array
    CMD=("$REAL_FFMPEG_PATH")

    # Add all original arguments to the array
    CMD+=("$@")

    # Add the special progress arguments for parsing
    CMD+=("-hide_banner" "-progress" "pipe:1")

    # Execute the command, redirect stderr to stdout, and pipe to parser
    # Use 'exec' to replace the current shell process with the ffmpeg command.
    # This is more efficient as it doesn't create an extra subshell.
    # Note: exec here means the script won't execute anything after this point.
    # If you need more complex post-processing in the wrapper, remove 'exec'.
    exec "${CMD[@]}" 2>&1 | "$PROGRESS_PARSER_CMD"
fi
