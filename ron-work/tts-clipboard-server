#!/home/<USER>/.pyenv/versions/3.12.11/envs/kokoro-tts/bin/python

import warnings

# Ignore warnings from upstream that I can't do anything about
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

from pathlib import Path
import logging

SOCKET_PATH = Path("/tmp/tts_clipboard.sock")
LOG_PATH = Path("/tmp/tts_clipboard.log")
preferred_voice = 'af_bella,af_jessica,am_michael,hf_alpha'

# Set up logging
logging.basicConfig(
    filename=LOG_PATH,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logging.info("Loading imports and setting up environment...")


import socket
import os
import signal
import sys
import json
from kokoro import KPipeline
import soundfile as sf
import vlc
import subprocess
import tempfile
import time
import threading
from queue import Queue


class TTSService:
    def __init__(self):
        self.pipeline = None
        self.instance = None
        self.current_player = None
        self.is_cancelled = False
        self.current_client = None
        self.current_generator = None
        self.processing_thread = None
        self.text_queue = Queue()
        self.client_lock = threading.Lock()  # Add lock for thread-safe client handling
        self.player_lock = threading.Lock()  # Add lock for player operations
        self.cleanup_event = threading.Event()  # Add event for coordinating cleanup

    def initialize(self):
        logging.info("Initializing TTS pipeline...")
        self.pipeline = KPipeline(lang_code='a', repo_id='hexgrad/Kokoro-82M')
        self.instance = vlc.Instance('--intf', 'dummy')
        logging.info("TTS service ready!")

    def cancel_playback(self):
        logging.info("Received cancel command - stopping playback...")
        self.is_cancelled = True
        self.cleanup_event.set()  # Signal that cleanup should happen

        with self.player_lock:
            if self.current_player:
                logging.info("Stopping current player...")
                try:
                    self.current_player.stop()
                except Exception as e:
                    logging.error(f"Error stopping player: {e}")

        # Clear the generator
        self.current_generator = None

        # Wait for processing thread to complete
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)
            self.processing_thread = None

    def cleanup_player(self, player):
        """Safely cleanup a player instance"""
        if not player:
            return

        with self.player_lock:
            try:
                player.stop()
                player.release()
                if self.current_player == player:
                    self.current_player = None
                logging.info("Player cleanup complete")
            except Exception as e:
                logging.error(f"Error during player cleanup: {e}")

    def process_text_worker(self, text, voice):
        local_player = None
        self.cleanup_event.clear()

        try:
            with tempfile.TemporaryDirectory(prefix='tts_') as temp_dir:
                if self.is_cancelled:
                    return

                # Use the split pattern to estimate total chunks
                import re
                split_pattern = r'[.!?](?=\s|$)'  # Same pattern used in pipeline
                text_chunks = [chunk.strip() for chunk in re.split(split_pattern, text) if chunk.strip()]
                total_chunks = len(text_chunks)
                buffer_size = max(1, int(total_chunks * 0.5))  # Pre-generate 50% of chunks

                logging.info(f"Text split into {total_chunks} chunks, buffering {buffer_size} chunks")

                with self.player_lock:
                    # Create a media player instead of a list player
                    player = self.instance.media_player_new()
                    self.current_player = player
                    local_player = player

                self.current_generator = self.pipeline(
                    text, voice=voice,
                    speed=1, split_pattern=split_pattern
                )

                # Buffer to store generated audio files
                audio_files = []

                # Pre-generate buffer_size chunks
                logging.debug("Buffering...")
                self.notify_status('buffering', {'progress': 0, 'total': buffer_size})

                for chunk_count, (gs, ps, audio) in enumerate(self.current_generator):
                    if self.is_cancelled:
                        logging.info(f"Processing cancelled during buffering at chunk {chunk_count}")
                        break

                    filename = os.path.join(temp_dir, f'{chunk_count}.wav')
                    sf.write(filename, audio, 24000)
                    audio_files.append(filename)

                    logging.debug(f"Buffered {chunk_count + 1}/{total_chunks}")

                    # Send buffering progress update
                    self.notify_status('buffering', {
                        'progress': chunk_count + 1,
                        'total': buffer_size,
                        'percentage': int((chunk_count + 1) / buffer_size * 100)
                    })

                    if chunk_count + 1 >= buffer_size:
                        break

                # Start playback of buffered chunks
                if audio_files and not self.is_cancelled:
                    # Notify client that playback is starting
                    self.notify_status('playback_started', {'msg': f"Part 1 of {total_chunks}" })

                    # Play first chunk
                    abs_path = os.path.abspath(audio_files[0])
                    with self.player_lock:
                        media = self.instance.media_new(abs_path)
                        local_player.set_media(media)
                        local_player.play()

                    # Continue generating and playing remaining chunks
                    current_chunk = 1  # Start from second chunk (index 1)

                    # Create a thread to continue buffering
                    buffer_thread = threading.Thread(
                        target=self._continue_buffering,
                        args=(self.current_generator, temp_dir, audio_files, buffer_size, total_chunks)
                    )
                    buffer_thread.daemon = True
                    buffer_thread.start()

                    # Play all chunks sequentially
                    logging.debug("Starting playback...")
                    while current_chunk < total_chunks and not self.is_cancelled:
                        with self.player_lock:
                            # wait for this chunk to finish or cancel is pressed
                            while local_player.get_state() not in [vlc.State.Ended, vlc.State.Stopped, vlc.State.Error]:
                                if self.cleanup_event.wait(0.1):  # Check for cleanup with timeout
                                    break

                            if self.is_cancelled:
                                break

                            # logging.debug(f"Checking chunk/available/total: {current_chunk}/{len(audio_files)}/{total_chunks}")
                            # Check if we have the next chunk available
                            if current_chunk < len(audio_files):
                                self.notify_status('playback_started', {'msg': f"Part {current_chunk + 1} of {total_chunks}" })
                                abs_path = os.path.abspath(audio_files[current_chunk])
                                media = self.instance.media_new(abs_path)
                                local_player.set_media(media)
                                local_player.play()
                                current_chunk += 1
                            else:
                                # Wait a bit for buffer thread to produce more chunks
                                time.sleep(0.1)

                    # Wait for final chunk to finish
                    if not self.is_cancelled and local_player:
                        logging.debug("Waiting for final chunk to finish...")
                        while local_player.get_state() not in [vlc.State.Ended, vlc.State.Stopped, vlc.State.Error]:
                            if self.cleanup_event.wait(0.1):
                                break

        except Exception as e:
            logging.error(f"Error processing text: {e}", exc_info=True)
        finally:
            if local_player:
                self.cleanup_player(local_player)
            self.notify_completion()

    def _continue_buffering(self, generator, temp_dir, audio_files, start_index, total_chunks):
        """Continue buffering audio chunks in a separate thread"""
        try:
            for chunk_count, (gs, ps, audio) in enumerate(generator, start=start_index):
                if self.is_cancelled:
                    logging.info(f"Buffering cancelled at chunk {chunk_count}")
                    break

                filename = os.path.join(temp_dir, f'{chunk_count}.wav')
                sf.write(filename, audio, 24000)

                # Thread-safe append to audio_files
                audio_files.append(filename)
                logging.debug(f"Buffered chunk {chunk_count+1}/{total_chunks}")
        except Exception as e:
            logging.error(f"Error in buffer thread: {e}", exc_info=True)

    def process_text(self, voice=preferred_voice):
        # Cancel any existing playback
        if self.processing_thread and self.processing_thread.is_alive():
            self.is_cancelled = True
            self.processing_thread.join(timeout=2.0)

        self.is_cancelled = False

        # xsel reads the clipboard (which needs a GUI)
        # if started headlessly, DISPLAY is missing. assume one exists on ":0"
        my_env = os.environ.copy()
        if "DISPLAY" not in my_env:
            my_env["DISPLAY"] = ":0"

        text = subprocess.check_output(['xsel'], env=my_env, universal_newlines=True)
        if not text.strip():
            logging.info("No text selected")
            self.notify_completion()
            return

        logging.info(f"Using voice({voice}): \n{text}")

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(
            target=self.process_text_worker,
            args=(text, voice)
        )
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def add_to_playlist(self, media_list, filename, text):
        logging.debug(f"Adding synthesized text: --> {text}")
        abs_path = os.path.abspath(filename)
        media = self.instance.media_new(abs_path)
        media_list.add_media(media)

    def notify_status(self, status, data=None):
        """Send status update to the client"""
        with self.client_lock:
            if self.current_client:
                try:
                    message = {'status': status}
                    if data:
                        message.update(data)
                    self.current_client.send(json.dumps(message).encode('utf-8'))
                except (BrokenPipeError, ConnectionResetError) as e:
                    logging.info(f"Client disconnected: {e}")
                except Exception as e:
                    logging.error(f"Error notifying client: {e}")

    def notify_completion(self):
        """Notify the client that playback is complete"""
        with self.client_lock:
            if self.current_client:
                try:
                    self.current_client.send(json.dumps({'status': 'complete'}).encode('utf-8'))
                except (BrokenPipeError, ConnectionResetError) as e:
                    logging.info(f"Client disconnected: {e}")
                except Exception as e:
                    logging.error(f"Error notifying client: {e}")
                finally:
                    try:
                        self.current_client.close()
                    except Exception:
                        pass
                    self.current_client = None

    def run(self):
        # Remove existing socket if present
        try:
            SOCKET_PATH.unlink()
        except FileNotFoundError:
            pass

        self.initialize()

        # Create Unix domain socket
        server = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        server.bind(str(SOCKET_PATH))
        server.listen(1)

        # Set socket permissions so other users can connect
        os.chmod(str(SOCKET_PATH), 0o777)

        logging.info(f"TTS service listening on {SOCKET_PATH}")

        while True:
            conn, addr = server.accept()
            try:
                data = conn.recv(1024).decode('utf-8')
                if not data:
                    conn.close()
                    continue

                request = json.loads(data)

                if request.get('command') == 'cancel':
                    logging.info("Received cancel command from client")
                    self.cancel_playback()
                    conn.close()
                elif request.get('command') == 'speak':
                    with self.client_lock:
                        # Close any existing client connection
                        if self.current_client:
                            try:
                                self.current_client.close()
                            except Exception:
                                pass
                        self.current_client = conn

                    voice = request.get('voice', preferred_voice)
                    self.process_text(voice)
                else:
                    conn.close()

            except json.JSONDecodeError:
                logging.error("Invalid JSON received")
                conn.close()
            except Exception as e:
                logging.error(f"Error processing request: {e}", exc_info=True)
                conn.close()

def cleanup(signum, frame):
    logging.info("Cleaning up...")
    try:
        SOCKET_PATH.unlink()
    except FileNotFoundError:
        pass
    sys.exit(0)

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, cleanup)
    signal.signal(signal.SIGTERM, cleanup)

    try:
        service = TTSService()
        service.run()
    except Exception as e:
        logging.critical(f"Fatal error: {e}", exc_info=True)
        cleanup(None, None)
