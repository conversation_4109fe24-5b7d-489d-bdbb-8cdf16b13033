NR==1 {
  printf "%-8s %-6s %-12s %-40s\n", "PID", "MEM%", "MEM", "COMMAND";
  next
}
NR<=51 {
  memKB=$6;
  command=$11;
  
  # Convert memory to the appropriate unit
  if (memKB >= 1048576) {
    mem = sprintf("%.2fGB", memKB / 1048576)
  } else if (memKB >= 1024) {
    mem = sprintf("%.2fMB", memKB / 1024)
  } else {
    mem = sprintf("%dKB", memKB)
  }
  
  # Print process details
  printf "%-8s %-6s %-12s %-40s\n", $2, $4, mem, command;
  
  # Aggregate memory usage by command
  memUsageByCommand[command] += memKB;
  
  # Count occurrences of each command
  countByCommand[command]++;
}
END {
  # Create an array to store both the command and memory usage
  for (command in memUsageByCommand) {
    totalMemKB = memUsageByCommand[command];
    summary[command] = totalMemKB;
  }

  # Sort the summary array by memory usage
  n = asorti(summary, sortedCommands, "@val_num_desc");

  printf "\n%-40s %-12s %-6s\n", "COMMAND", "TOTAL MEM", "PROCESSES";
  for (i = 1; i <= n; i++) {
    command = sortedCommands[i];
    totalMemKB = summary[command];
    
    # Convert aggregated memory to MB or GB
    if (totalMemKB >= 1048576) {
      totalMem = sprintf("%.2fGB", totalMemKB / 1048576);
    } else {
      totalMem = sprintf("%.2fMB", totalMemKB / 1024);
    }
    
    # Print sorted summary
    printf "%-40s %-12s %-6d\n", command, totalMem, countByCommand[command];
  }
}
