#!/bin/bash

#set -x

# --- Configuration ---
# Paths to the *real* ffmpeg and ffprobe binaries. Adjust if needed.
REAL_FFMPEG_PATH="/usr/bin/ffmpeg"
REAL_FFPROBE_PATH="/usr/bin/ffprobe"

# Special arguments for piped output.
PROGRESS_ARGS=("-progress" "pipe:1" "-nostats" "-loglevel" "quiet")

# Name of your progress parser script (must be executable in PATH)
PROGRESS_PARSER_CMD="ffmpeg_progress_parser"

# --- Wrapper Logic ---

# Check if stdout of *this wrapper script* is connected to a TTY.
if ! [ -t 1 ]; then
    # --- Output is piped or redirected. ---

    # 1. Extract input file from original arguments to probe its duration.
    # These variables are now global to the script, not local.
    INPUT_FILE_PATH=""
    found_i=false
    for arg in "$@"; do
        if $found_i; then INPUT_FILE_PATH="$arg"; break; fi
        if [[ "$arg" == "-i" ]]; then found_i=true; fi
    done

    if [[ -z "$INPUT_FILE_PATH" ]]; then
        echo "Wrapper Error: Could not find input file (-i) for duration pre-parsing." >&2
        # Fallback to normal ffmpeg without progress parsing
        exec "$REAL_FFMPEG_PATH" "$@"
    fi

    # 2. Get total duration in seconds using ffprobe. This is very fast.
    # These variables are now global to the script, not local.
    TOTAL_SECONDS_DURATION=""
    DURATION_LINE=""
    DURATION_LINE=$("$REAL_FFPROBE_PATH" \
        -v error \
        -show_entries format=duration \
        -of default=noprint_wrappers=1:nokey=1 \
        "$INPUT_FILE_PATH")
    # ffprobe directly outputs the seconds, so no need for time_to_seconds helper here.
    TOTAL_SECONDS_DURATION="$DURATION_LINE"

    # 3. Run the main ffmpeg command with clean progress args and pipe to parser.
    CMD=("$REAL_FFMPEG_PATH" "$@" "${PROGRESS_ARGS[@]}")
    
    # Pass the found duration to the parser as its first argument.
    exec "${CMD[@]}" 2>&1 | "$PROGRESS_PARSER_CMD" "$TOTAL_SECONDS_DURATION"

else
    # --- Output is to a TTY. Run ffmpeg normally. ---
    # Added -hide_banner for a cleaner default interactive experience.
    exec "$REAL_FFMPEG_PATH" -hide_banner "$@"
fi
