#!/usr/bin/env bash
# Save as /home/<USER>/bin/ocrscr-launcher

# Log the start
echo "Launcher started with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt

# Use yad to create a button that will trigger the OCR script
yad --title="OCR Screen Capture" \
    --text="Click 'Capture' to select a screen area for OCR" \
    --button="Capture:0" \
    --center \
    --borders=10

# If yad returns success (button was clicked), run the worker script
if [ $? -eq 0 ]; then
    /home/<USER>/bin/ocrscr-worker
fi
