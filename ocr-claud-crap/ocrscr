#!/usr/bin/env bash
# Dependencies: tesseract-ocr imagemagick maim slop xsel
echo "Script started with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt
export tesseract_lang=eng
SCR_IMG=`mktemp`
trap "rm $SCR_IMG*;echo '<PERSON>ript trapped at DISPLAY=$DISPLAY at $(date)' >> ~/script_log.txt" EXIT

# Notify user that selection is about to happen
notify-send "OCR Screen Capture" "Click and drag to select screen area for OCR" -t 3000

# Use slop and maim instead of scrot
sleep 1
SELECTION=$(slop -f "%x %y %w %h")
read -r X Y W H < <(echo $SELECTION)
echo "Selection coordinates: X=$X Y=$Y W=$W H=$H" >> ~/script_log.txt
maim -g "${W}x${H}+${X}+${Y}" $SCR_IMG.png

# Continue with the rest of your script
mogrify -modulate 100,0 -resize 400% $SCR_IMG.png 
tesseract $SCR_IMG.png $SCR_IMG &> /dev/null
cat $SCR_IMG.txt | xsel -bi
echo "Script ended with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt
exit
