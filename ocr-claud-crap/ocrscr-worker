#!/usr/bin/env bash
# Save this as /home/<USER>/bin/ocrscr-worker
# Dependencies: tesseract-ocr imagemagick scrot xsel

sleep .2

echo "WORKER started with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt
export tesseract_lang=eng
SCR_IMG=`mktemp`
trap "rm $SCR_IMG*;echo 'WORKER trapped at DISPLAY=$DISPLAY at $(date)' >> ~/script_log.txt" EXIT

# Now run scrot in selection mode
scrot -s $SCR_IMG.png -q 100    

# Process the image
mogrify -modulate 100,0 -resize 400% $SCR_IMG.png 
tesseract $SCR_IMG.png $SCR_IMG &> /dev/null
cat $SCR_IMG.txt | xsel -bi
echo "WORKER ended with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt
exit
