#!/usr/bin/env bash
# Save as /home/<USER>/bin/ocrscr-silent

# Log the start
echo "SILENT launcher started with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt

# Show a notification that capture is about to start
#notify-send "OCR Screen Capture" "Select an area to capture..." --icon=accessories-screenshot --urgency=critical --expire-time=2000

# Give a moment for the notification to be seen
sleep .2

# Now directly run the worker script that handles the capture
/home/<USER>/bin/ocrscr-worker

echo "SILENT launcher ended with DISPLAY=$DISPLAY at $(date)" >> ~/script_log.txt
